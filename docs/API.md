# API Reference

This document provides detailed API documentation for the web-fetch library.

## Core Classes

### WebFetcher

The main async web fetcher class with session management and modern Python features.

```python
from web_fetch import WebFetcher, FetchConfig, FetchRequest

config = FetchConfig(
    total_timeout=30.0,
    max_concurrent_requests=10,
    max_retries=3
)

async with WebFetcher(config) as fetcher:
    request = FetchRequest(url="https://api.example.com/data")
    result = await fetcher.fetch_single(request)
```

#### Methods

- `fetch_single(request: FetchRequest) -> FetchResult`
- `fetch_batch(batch_request: BatchFetchRequest) -> BatchFetchResult`

### StreamingWebFetcher

Extended WebFetcher with streaming capabilities for large files.

```python
from web_fetch import StreamingWebFetcher, StreamRequest
from pathlib import Path

async with StreamingWebFetcher() as fetcher:
    request = StreamRequest(
        url="https://example.com/large-file.zip",
        output_path=Path("downloads/file.zip")
    )
    result = await fetcher.stream_fetch(request)
```

### FTPFetcher

Async FTP client with comprehensive functionality.

```python
from web_fetch.ftp import FTPFetcher, FTPConfig, FTPRequest

config = FTPConfig(
    host="ftp.example.com",
    username="user",
    password="pass"
)

async with FTPFetcher(config) as ftp:
    request = FTPRequest(
        remote_path="/path/to/file.txt",
        local_path="downloads/file.txt"
    )
    result = await ftp.download_file(request)
```

## Configuration Classes

### FetchConfig

Main configuration for HTTP operations.

```python
from web_fetch import FetchConfig, RetryStrategy

config = FetchConfig(
    total_timeout=30.0,           # Total request timeout
    connect_timeout=10.0,         # Connection timeout
    read_timeout=20.0,            # Read timeout
    max_concurrent_requests=10,   # Concurrency limit
    max_retries=3,                # Retry attempts
    retry_delay=1.0,              # Base retry delay
    retry_strategy=RetryStrategy.EXPONENTIAL,
    max_response_size=10*1024*1024,  # Max response size
    verify_ssl=True,              # SSL verification
    follow_redirects=True         # Follow redirects
)
```

### StreamingConfig

Configuration for streaming operations.

```python
from web_fetch import StreamingConfig

config = StreamingConfig(
    chunk_size=16384,           # 16KB chunks
    enable_progress=True,       # Enable progress tracking
    max_file_size=100*1024*1024 # 100MB limit
)
```

### FTPConfig

Configuration for FTP operations.

```python
from web_fetch.ftp import FTPConfig, FTPMode, FTPAuthType

config = FTPConfig(
    host="ftp.example.com",
    port=21,
    username="user",
    password="pass",
    mode=FTPMode.PASSIVE,
    auth_type=FTPAuthType.PASSWORD,
    timeout=30.0,
    max_connections=5
)
```

## Request Classes

### FetchRequest

HTTP request specification.

```python
from web_fetch import FetchRequest, ContentType

request = FetchRequest(
    url="https://api.example.com/data",
    method="POST",                 # HTTP method
    headers={"Authorization": "Bearer token"},
    data={"key": "value"},        # Request data
    content_type=ContentType.JSON, # Content parsing type
    timeout_override=15.0         # Override default timeout
)
```

### StreamRequest

Streaming request specification.

```python
from web_fetch import StreamRequest, StreamingConfig
from pathlib import Path

request = StreamRequest(
    url="https://example.com/large-file.zip",
    output_path=Path("downloads/file.zip"),
    streaming_config=StreamingConfig(chunk_size=32768),
    resume_download=True,         # Resume partial downloads
    verify_checksum=True          # Verify file integrity
)
```

### FTPRequest

FTP operation request.

```python
from web_fetch.ftp import FTPRequest, FTPTransferMode

request = FTPRequest(
    remote_path="/remote/file.txt",
    local_path="local/file.txt",
    transfer_mode=FTPTransferMode.BINARY,
    create_directories=True,      # Create local dirs if needed
    overwrite_existing=False      # Don't overwrite existing files
)
```

## Result Classes

### FetchResult

HTTP response result.

```python
result = await fetcher.fetch_single(request)

print(f"Success: {result.is_success}")
print(f"Status: {result.status_code}")
print(f"Content: {result.content}")
print(f"Headers: {result.headers}")
print(f"Response time: {result.response_time}")
print(f"Error: {result.error}")
```

### StreamResult

Streaming operation result.

```python
result = await fetcher.stream_fetch(request)

print(f"Success: {result.is_success}")
print(f"Bytes downloaded: {result.bytes_downloaded}")
print(f"File path: {result.file_path}")
print(f"Checksum: {result.checksum}")
```

### FTPResult

FTP operation result.

```python
result = await ftp.download_file(request)

print(f"Success: {result.is_success}")
print(f"Bytes transferred: {result.bytes_transferred}")
print(f"Transfer speed: {result.transfer_speed}")
print(f"File info: {result.file_info}")
```

## Utility Functions

### URL Utilities

```python
from web_fetch import is_valid_url, normalize_url, analyze_url

# Validate URLs
valid = is_valid_url("https://example.com")

# Normalize URLs
normalized = normalize_url("HTTPS://EXAMPLE.COM/path/../other?b=2&a=1")

# Analyze URLs
analysis = analyze_url("https://example.com:8080/path")
print(f"Domain: {analysis.domain}")
print(f"Secure: {analysis.is_secure}")
print(f"Port: {analysis.port}")
```

### Response Analysis

```python
from web_fetch import analyze_headers, detect_content_type

# Analyze response headers
header_analysis = analyze_headers(response_headers)
print(f"Cacheable: {header_analysis.is_cacheable}")
print(f"Security headers: {header_analysis.has_security_headers}")

# Detect content type
content_type = detect_content_type(headers, content_bytes)
```

### Caching

```python
from web_fetch import fetch_with_cache, CacheConfig

cache_config = CacheConfig(
    max_size=100,              # Max 100 entries
    ttl_seconds=300,           # 5 minute TTL
    enable_compression=True    # Compress cached data
)

result = await fetch_with_cache(url, cache_config=cache_config)
```

## Enums

### ContentType

- `ContentType.TEXT` - Parse as UTF-8 text
- `ContentType.JSON` - Parse as JSON object
- `ContentType.HTML` - Parse HTML and extract structured data
- `ContentType.RAW` - Return raw bytes

### RetryStrategy

- `RetryStrategy.EXPONENTIAL` - Exponential backoff (default)
- `RetryStrategy.LINEAR` - Linear backoff
- `RetryStrategy.NONE` - No retries

### FTPMode

- `FTPMode.ACTIVE` - Active FTP mode
- `FTPMode.PASSIVE` - Passive FTP mode (default)

### FTPAuthType

- `FTPAuthType.ANONYMOUS` - Anonymous login
- `FTPAuthType.PASSWORD` - Username/password authentication
- `FTPAuthType.KEY` - Key-based authentication

## Exception Hierarchy

```
WebFetchError
├── NetworkError
├── TimeoutError
├── ConnectionError
├── HTTPError
│   ├── AuthenticationError (401, 403)
│   ├── NotFoundError (404)
│   ├── RateLimitError (429)
│   └── ServerError (5xx)
├── ContentError
└── FTPError
    ├── FTPConnectionError
    ├── FTPAuthenticationError
    ├── FTPTimeoutError
    ├── FTPTransferError
    ├── FTPFileNotFoundError
    ├── FTPPermissionError
    ├── FTPVerificationError
    └── FTPProtocolError
```
