[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    -v
    --tb=short
    --strict-markers
    --strict-config
    --cov=web_fetch
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    --durations=10
asyncio_mode = auto
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests requiring network access
    unit: marks tests as unit tests (fast, no external dependencies)
    ftp: marks tests as FTP-related tests
    http: marks tests as HTTP-related tests
    cli: marks tests as CLI-related tests
    performance: marks tests as performance/benchmark tests
