"""
Circuit breaker pattern implementation for resilient web fetching.

This module provides a circuit breaker to prevent cascading failures when
external services are experiencing issues.
"""

from __future__ import annotations

import asyncio
import time
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Awaitable, Callable, Dict, Optional
from urllib.parse import urlparse


class CircuitState(str, Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, requests blocked
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker behavior."""
    
    failure_threshold: int = 5  # Number of failures before opening
    recovery_timeout: float = 60.0  # Seconds before trying half-open
    success_threshold: int = 3  # Successes needed to close from half-open
    timeout: float = 30.0  # Request timeout in seconds
    
    # What constitutes a failure
    failure_exceptions: tuple = (Exception,)
    failure_status_codes: set = field(default_factory=lambda: {500, 502, 503, 504})


@dataclass
class CircuitBreakerStats:
    """Statistics for circuit breaker monitoring."""
    
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    blocked_requests: int = 0
    state_changes: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None


class CircuitBreakerError(Exception):
    """Raised when circuit breaker is open and blocking requests."""
    
    def __init__(self, message: str, stats: CircuitBreakerStats) -> None:
        super().__init__(message)
        self.stats = stats


class CircuitBreaker:
    """
    Circuit breaker implementation for resilient service calls.
    
    Prevents cascading failures by temporarily blocking requests to failing services
    and allowing them to recover.
    """
    
    def __init__(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> None:
        """
        Initialize circuit breaker.
        
        Args:
            name: Unique name for this circuit breaker
            config: Configuration options
        """
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.state = CircuitState.CLOSED
        self.stats = CircuitBreakerStats()
        
        self._failure_count = 0
        self._success_count = 0
        self._last_failure_time = 0.0
        self._lock = asyncio.Lock()
    
    async def call(
        self,
        func: Callable[..., Awaitable[Any]] | Callable[..., Any],
        *args: Any,
        **kwargs: Any
    ) -> Any:
        """
        Execute a function call through the circuit breaker.
        
        Args:
            func: Function to call
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Result of the function call
            
        Raises:
            CircuitBreakerError: If circuit is open
            Any exception raised by the function
        """
        async with self._lock:
            self.stats.total_requests += 1
            
            # Check if circuit should be opened
            if self.state == CircuitState.CLOSED and self._should_open():
                await self._open_circuit()
            
            # Check if circuit should transition to half-open
            elif self.state == CircuitState.OPEN and self._should_attempt_reset():
                await self._half_open_circuit()
            
            # Block requests if circuit is open
            if self.state == CircuitState.OPEN:
                self.stats.blocked_requests += 1
                raise CircuitBreakerError(
                    f"Circuit breaker '{self.name}' is OPEN. Service appears to be failing.",
                    self.stats
                )
        
        # Execute the function call
        try:
            # Apply timeout if configured
            if self.config.timeout > 0:
                result = await asyncio.wait_for(
                    func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else asyncio.to_thread(func, *args, **kwargs),
                    timeout=self.config.timeout
                )
            else:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = await asyncio.to_thread(func, *args, **kwargs)
            
            # Handle successful call
            await self._on_success()
            return result
            
        except Exception as e:
            # Handle failed call
            await self._on_failure(e)
            raise
    
    def _should_open(self) -> bool:
        """Check if circuit should be opened due to failures."""
        return self._failure_count >= self.config.failure_threshold
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit should attempt to reset from open to half-open."""
        return (time.time() - self._last_failure_time) >= self.config.recovery_timeout
    
    async def _open_circuit(self) -> None:
        """Transition circuit to OPEN state."""
        self.state = CircuitState.OPEN
        self.stats.state_changes += 1
        self._last_failure_time = time.time()
    
    async def _half_open_circuit(self) -> None:
        """Transition circuit to HALF_OPEN state."""
        self.state = CircuitState.HALF_OPEN
        self.stats.state_changes += 1
        self._success_count = 0
    
    async def _close_circuit(self) -> None:
        """Transition circuit to CLOSED state."""
        self.state = CircuitState.CLOSED
        self.stats.state_changes += 1
        self._failure_count = 0
        self._success_count = 0
    
    async def _on_success(self) -> None:
        """Handle successful function call."""
        async with self._lock:
            self.stats.successful_requests += 1
            self.stats.last_success_time = datetime.now()
            
            if self.state == CircuitState.HALF_OPEN:
                self._success_count += 1
                if self._success_count >= self.config.success_threshold:
                    await self._close_circuit()
            elif self.state == CircuitState.CLOSED:
                self._failure_count = max(0, self._failure_count - 1)
    
    async def _on_failure(self, exception: Exception) -> None:
        """Handle failed function call."""
        async with self._lock:
            # Check if this exception should count as a failure
            if self._is_failure(exception):
                self.stats.failed_requests += 1
                self.stats.last_failure_time = datetime.now()
                self._failure_count += 1
                self._last_failure_time = time.time()
                
                if self.state == CircuitState.HALF_OPEN:
                    await self._open_circuit()
    
    def _is_failure(self, exception: Exception) -> bool:
        """Determine if an exception should be counted as a failure."""
        # Check exception type
        if isinstance(exception, self.config.failure_exceptions):
            return True
        
        # Check HTTP status codes if available
        status = getattr(exception, "status_code", None)
        if status is not None:
            try:
                code = int(status)
            except (TypeError, ValueError):
                return False
            return code in self.config.failure_status_codes
        
        return False
    
    def get_stats(self) -> CircuitBreakerStats:
        """Get current circuit breaker statistics."""
        return self.stats
    
    def reset(self) -> None:
        """Manually reset the circuit breaker to CLOSED state."""
        self.state = CircuitState.CLOSED
        self._failure_count = 0
        self._success_count = 0
        self.stats.state_changes += 1


class CircuitBreakerRegistry:
    """Registry for managing multiple circuit breakers by service/host."""
    
    def __init__(self) -> None:
        self._breakers: Dict[str, CircuitBreaker] = {}
        self._lock = asyncio.Lock()
    
    async def get_breaker(self, url: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """
        Get or create a circuit breaker for a URL/service.
        
        Args:
            url: URL to get circuit breaker for
            config: Optional configuration for new circuit breakers
            
        Returns:
            CircuitBreaker instance for the service
        """
        # Extract host from URL for circuit breaker key
        try:
            parsed = urlparse(url)
            key = f"{parsed.scheme}://{parsed.netloc}"
        except Exception:
            key = url
        
        async with self._lock:
            if key not in self._breakers:
                self._breakers[key] = CircuitBreaker(key, config)
            return self._breakers[key]
    
    def get_all_stats(self) -> Dict[str, CircuitBreakerStats]:
        """Get statistics for all circuit breakers."""
        return {name: breaker.get_stats() for name, breaker in self._breakers.items()}


# Global registry instance
_global_registry = CircuitBreakerRegistry()


async def with_circuit_breaker(
    url: str,
    func: Callable[..., Awaitable[Any]] | Callable[..., Any],
    *args: Any,
    config: Optional[CircuitBreakerConfig] = None,
    **kwargs: Any
) -> Any:
    """
    Convenience function to execute a function with circuit breaker protection.
    
    Args:
        url: URL/service identifier for circuit breaker
        func: Function to execute
        *args: Positional arguments for function
        config: Optional circuit breaker configuration
        **kwargs: Keyword arguments for function
        
    Returns:
        Result of function execution
    """
    breaker = await _global_registry.get_breaker(url, config)
    return await breaker.call(func, *args, **kwargs)


__all__ = [
    "CircuitBreaker",
    "CircuitBreakerConfig", 
    "CircuitBreakerError",
    "CircuitBreakerRegistry",
    "CircuitBreakerStats",
    "CircuitState",
    "with_circuit_breaker",
]
