[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black)
    W503,
    # E501: line too long (handled by black)
    E501,
    # F401: imported but unused (handled by autoflake)
    F401,
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    htmlcov,
    .pytest_cache,
    .mypy_cache,
per-file-ignores =
    # __init__.py files can have unused imports
    __init__.py:F401,F403
    # Test files can have unused imports and long lines
    tests/*:F401,F403,E501
    # Examples can be more relaxed
    examples/*:E501,F401
max-complexity = 10
docstring-convention = google
# Ignore specific error codes for certain patterns
ignore-names = 
    setUp,
    tearDown,
    setUpClass,
    tearDownClass,
    setUpModule,
    tearDownModule,
    asyncSetUp,
    asyncTearDown
